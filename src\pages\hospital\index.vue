<script setup lang="ts">
import {Document, Menu as IconMenu, Setting } from '@element-plus/icons-vue'
</script>

<template>
  <div class="hospital">
    <el-menu
        default-active="2"
        class="navigator"
        @open="handleOpen"
        @close="handleClose"
      >
        <el-menu-item index="1">
          <el-icon><icon-menu /></el-icon>
          <span>Navigator One</span>
        </el-menu-item>
        <el-menu-item index="2">
          <el-icon><icon-menu /></el-icon>
          <span>Navigator Two</span>
        </el-menu-item>
        <el-menu-item index="3">
          <el-icon><document /></el-icon>
          <span>Navigator Three</span>
        </el-menu-item>
        <el-menu-item index="4">
          <el-icon><setting /></el-icon>
          <span>Navigator Four</span>
        </el-menu-item>
      </el-menu>
    <div class="content">

    </div>
  </div>
</template>

<style scoped lang="scss">
.hospital {
  display: flex;
  .navigator {
    flex: 2;
  }
  .content {
    flex: 8;
  }
}
</style>